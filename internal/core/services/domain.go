package services

import (
	"errors"
	"fmt"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
)

type DomainService struct {
	domainRepo         ports.DomainRepository
	namespaceRepo      ports.NamespaceRepository
	IngressSpecService ports.IngressSpecService
	DnsService         ports.DnsService
	OperationService   ports.OperationService
}

func NewDomainService(
	domainRepo ports.DomainRepository,
	namespaceRepo ports.NamespaceRepository,
	ingressSpecService ports.IngressSpecService,
	dnsService ports.DnsService,
	operationService ports.OperationService,
) ports.DomainService {
	return &DomainService{
		domainRepo:         domainRepo,
		namespaceRepo:      namespaceRepo,
		IngressSpecService: ingressSpecService,
		DnsService:         dnsService,
		OperationService:   operationService,
	}
}

func (s *DomainService) Create(name string, isDefault, isActive bool, zoneID, accountID, accountName string, namespaceID uint64) (*domain.Domain, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if zoneID == "" {
		return nil, errors.New("zone ID is required")
	}
	if accountID == "" {
		return nil, errors.New("account ID is required")
	}
	if accountName == "" {
		return nil, errors.New("account name is required")
	}
	if namespaceID == 0 {
		return nil, errors.New("namespace ID is required")
	}

	// Verify namespace exists
	_, err := s.namespaceRepo.FindByID(namespaceID)
	if err != nil {
		return nil, errors.New("namespace not found")
	}

	// Calculate the next index for this namespace
	filter := &ports.DomainFilter{
		NamespaceID: &namespaceID,
	}
	existingDomains, err := s.domainRepo.FindAll(filter)
	if err != nil {
		return nil, err
	}

	// Find the maximum index value
	maxIndex := 0
	for _, d := range existingDomains {
		if d.Index > maxIndex {
			maxIndex = d.Index
		}
	}

	// Set the new dns's index to max + 1
	nextIndex := maxIndex + 1

	domain := &domain.Domain{
		Name:        name,
		IsDefault:   isDefault,
		IsActive:    isActive,
		ZoneID:      zoneID,
		AccountID:   accountID,
		AccountName: accountName,
		NamespaceID: namespaceID,
		Index:       nextIndex,
	}

	err = s.domainRepo.Insert(domain)
	if err != nil {
		return nil, err
	}

	return domain, nil
}

func (s *DomainService) GetAll(filter *ports.DomainFilter) ([]*domain.Domain, error) {
	return s.domainRepo.FindAll(filter)
}

func (s *DomainService) GetByID(id uint64) (*domain.Domain, error) {
	return s.domainRepo.FindByID(id)
}

func (s *DomainService) GetDefaultByNamespaceID(namespaceID uint64) (*domain.Domain, error) {
	if namespaceID == 0 {
		return nil, errors.New("namespace ID is required")
	}

	// Verify namespace exists
	_, err := s.namespaceRepo.FindByID(namespaceID)
	if err != nil {
		return nil, errors.New("namespace not found")
	}

	// Create filter to find default domain in the namespace
	isDefault := true
	filter := &ports.DomainFilter{
		NamespaceID: &namespaceID,
		IsDefault:   &isDefault,
	}

	domains, err := s.domainRepo.FindAll(filter)
	if err != nil {
		return nil, err
	}

	if len(domains) == 0 {
		return nil, errors.New("no default domain found for this namespace")
	}

	// Return the first (and should be only) default domain
	return domains[0], nil
}

func (s *DomainService) Update(id uint64, name string, isDefault, isActive bool, zoneID, accountID, accountName string, namespaceID uint64, index int) (*domain.Domain, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if zoneID == "" {
		return nil, errors.New("zone ID is required")
	}
	if accountID == "" {
		return nil, errors.New("account ID is required")
	}
	if accountName == "" {
		return nil, errors.New("account name is required")
	}
	if namespaceID == 0 {
		return nil, errors.New("namespace ID is required")
	}

	// Verify namespace exists
	_, err := s.namespaceRepo.FindByID(namespaceID)
	if err != nil {
		return nil, errors.New("namespace not found")
	}

	// Find existing dns
	domain, err := s.domainRepo.FindByID(id)
	if err != nil {
		return nil, err
	}

	// Update fields
	domain.Name = name
	domain.IsDefault = isDefault
	domain.IsActive = isActive
	domain.ZoneID = zoneID
	domain.AccountID = accountID
	domain.AccountName = accountName
	domain.NamespaceID = namespaceID
	domain.Index = index

	err = s.domainRepo.Update(domain)
	if err != nil {
		return nil, err
	}

	return domain, nil
}

func (s *DomainService) UpdateStatus(id uint64, isActive bool) (*domain.Domain, error) {
	// Find existing dns
	domain, err := s.domainRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("dns not found")
	}

	// Update only the is_active field
	domain.IsActive = isActive

	err = s.domainRepo.Update(domain)
	if err != nil {
		return nil, err
	}

	return domain, nil
}

func (s *DomainService) SetDefault(userID, id uint64, accessToken string) (*domain.Domain, error) {
	// Find the target dns
	targetDomain, err := s.domainRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("dns not found")
	}

	// Get all domains in the same namespace
	filter := &ports.DomainFilter{
		NamespaceID: &targetDomain.NamespaceID,
	}
	domainsInNamespace, err := s.domainRepo.FindAll(filter)
	if err != nil {
		return nil, err
	}

	// Set is_default = false for all other domains in the namespace
	for _, d := range domainsInNamespace {
		if d.ID != id && d.IsDefault {
			d.IsDefault = false
			err = s.domainRepo.Update(d)
			if err != nil {
				return nil, err
			}
		}
	}

	// Set is_default = true for the target dns
	targetDomain.IsDefault = true
	err = s.domainRepo.Update(targetDomain)
	if err != nil {
		return nil, err
	}

	// Update all ingress-specs in the namespace to use the new default dns
	_, err = s.IngressSpecService.UpdateHostsByNamespace(targetDomain.NamespaceID, targetDomain.Name)
	if err != nil {
		return nil, err
	}

	namespace, err := s.namespaceRepo.FindByID(targetDomain.NamespaceID)
	if err != nil {
		return nil, errors.New("namespace not found")
	}

	var req dto.OperationCreateReq
	req = dto.OperationCreateReq{
		ClusterID:   namespace.ClusterID,
		NamespaceID: targetDomain.NamespaceID,
		Method:      "apply",
	}

	operation, err := s.OperationService.CreateOperationAsync(userID, accessToken, req)
	if err != nil {
		return nil, err
	}
	fmt.Printf("update operation starter : %v\n", operation)

	_, err = s.DnsService.HandleDnsAsync(accessToken, dto.HandleDnsRequest{
		NamespaceID: targetDomain.NamespaceID,
		ZoneID:      targetDomain.ZoneID,
		Method:      "apply",
	})
	if err != nil {
		return nil, err
	}

	return targetDomain, nil
}

func (s *DomainService) Delete(id uint64) error {
	return s.domainRepo.Delete(id)
}
